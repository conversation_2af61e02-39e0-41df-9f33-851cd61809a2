"use client"

import Image from "next/image"
import { X, Star } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogOverlay } from "@/components/ui/dialog"

interface LoanDetails {
  loanAmount: string
  repaymentPeriod: string
  monthlyRepayment: string
  processingFee: string
  latePaymentFee: string
  minCreditScore: string
  stableIncome: string
  employmentHistory: string
  approvalTime: string
  lenderRating: number
  reviewCount: number
}

interface LoanOffer {
  id: string
  bankName: string
  logo: string
  rating: number
  amount: string
  interestRate: string
  duration: string
  collateral: string
  documents: string
  details: LoanDetails
}

interface LoanDetailsPopupProps {
  isOpen: boolean
  onClose: () => void
  loanOffer: LoanOffer | null
}

export default function LoanDetailsPopup({ isOpen, onClose, loanOffer }: LoanDetailsPopupProps) {
  if (!loanOffer) return null

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        style={{
          width: "16px",
          height: "16px",
          fill: i < rating ? "#facc15" : "transparent",
          color: i < rating ? "#facc15" : "#d1d5db",
        }}
      />
    ))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogOverlay className="fixed inset-0 bg-black/50 z-50" />
      <DialogContent className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[90vw] max-w-[600px] max-h-[90vh] bg-white rounded-[24px] p-0 border-none shadow-2xl z-51 overflow-hidden">
        <div className="h-full max-h-[90vh] overflow-y-auto p-8 pb-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Image 
                src="/Loan.svg" 
                alt="Loan Icon" 
                width={32} 
                height={32}
                className="w-8 h-8"
              />
              <h2 className="text-2xl font-bold text-[#1a1a1a] m-0 leading-tight">
                Loan Offer Details
              </h2>
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              size="icon"
              className="p-2 bg-transparent border-none cursor-pointer rounded-lg flex items-center justify-center"
            >
              <X className="w-6 h-6 text-gray-500" />
            </Button>
          </div>

          {/* Loan Details */}
          <div className="flex flex-col gap-6 mb-8">
            <div className="flex flex-col gap-4">
              {[
                { label: "Loan Amount", value: loanOffer.details.loanAmount },
                { label: "Repayment Period", value: loanOffer.details.repaymentPeriod },
                { label: "Monthly Repayment", value: loanOffer.details.monthlyRepayment },
                { label: "Processing Fee", value: loanOffer.details.processingFee },
                { label: "Late Payment Fee", value: loanOffer.details.latePaymentFee },
              ].map((item, index) => (
                <div
                  key={index}
                  className="flex justify-between items-start gap-4"
                >
                  <span className="text-base text-[#1a1a1a] leading-normal flex-1">
                    {item.label}
                  </span>
                  <span className="text-base font-semibold text-[#1a1a1a] leading-normal text-right">
                    {item.value}
                  </span>
                </div>
              ))}
            </div>

            <div className="flex flex-col gap-4">
              {[
                { label: "Minimum Credit Score", value: loanOffer.details.minCreditScore },
                { label: "Stable Income", value: loanOffer.details.stableIncome },
                { label: "Employment History", value: loanOffer.details.employmentHistory },
              ].map((item, index) => (
                <div
                  key={index}
                  className="flex justify-between items-start gap-4"
                >
                  <span className="text-base text-[#1a1a1a] leading-normal flex-1">
                    {item.label}
                  </span>
                  <span className="text-base font-semibold text-[#1a1a1a] leading-normal text-right">
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Lender Information */}
          <div className="mb-8">
            <h3 className="text-xl font-bold text-[#1a1a1a] mb-2 leading-snug">
              Lender Information
            </h3>
            <p className="text-sm text-gray-500 mb-3 leading-normal">
              Lender Rating
            </p>
            <div className="flex items-center gap-2 mb-2 flex-wrap">
              <div className="flex gap-1">{renderStars(loanOffer.details.lenderRating)}</div>
            </div>
            <p className="text-sm text-gray-500 leading-normal">
              (Based on {loanOffer.details.reviewCount} reviews)
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
