"use client"

import { Search as SearchIcon } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useEffect, useRef, useState } from "react"

export default function LoanSearchInterface() {
  const [isMobile, setIsMobile] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    handleResize() // Initial check
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div style={{
      width: '100%',
      maxWidth: '1523px',
      margin: '0 auto',
      borderRadius: '20px',
      border: '1px solid #8D938CB2',
      color: '#0B0A0A',
      padding: '24px',
      overflow: 'hidden'
    }}>
      <div 
        ref={containerRef}
        style={{
          overflowX: 'auto',
          paddingBottom: '8px', // For scrollbar space
        }}
      >
        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : 'repeat(auto-fit, minmax(180px, 1fr))',
          gap: '16px',
          minWidth: isMobile ? 'auto' : '1200px',
        }}>
          {/* Search Field */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>Search</label>
            <div style={{ position: 'relative' }}>
              <Input 
                type="text" 
                placeholder="Search" 
                style={{
                  paddingLeft: '1rem',
                  paddingRight: '2.5rem',
                  height: '3rem',
                  borderColor: '#D1D5DB',
                  borderRadius: '0.5rem'
                }}
              />
              <SearchIcon style={{
                position: 'absolute',
                right: '0.75rem',
                top: '50%',
                transform: 'translateY(-50%)',
                height: '1.25rem',
                width: '1.25rem',
                color: '#9CA3AF'
              }} />
            </div>
          </div>

          {/* Loan Amount */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>Loan Amount (N)</label>
            <Select>
              <SelectTrigger style={{
                height: '3rem',
                borderColor: '#D1D5DB',
                borderRadius: '0.5rem'
              }}>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="any">Any</SelectItem>
                <SelectItem value="100k-500k">₦100k - ₦500k</SelectItem>
                <SelectItem value="500k-1m">₦500k - ₦1M</SelectItem>
                <SelectItem value="1m-5m">₦1M - ₦5M</SelectItem>
                <SelectItem value="5m+">₦5M+</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Interest Rate */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>Interest Rate</label>
            <Select>
              <SelectTrigger style={{
                height: '3rem',
                borderColor: '#D1D5DB',
                borderRadius: '0.5rem'
              }}>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="any">Any</SelectItem>
                <SelectItem value="0-5">0% - 5%</SelectItem>
                <SelectItem value="5-10">5% - 10%</SelectItem>
                <SelectItem value="10-15">10% - 15%</SelectItem>
                <SelectItem value="15+">15%+</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Duration/Tenure */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>Duration/Tenure</label>
            <Select>
              <SelectTrigger style={{
                height: '3rem',
                borderColor: '#D1D5DB',
                borderRadius: '0.5rem'
              }}>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="any">Any</SelectItem>
                <SelectItem value="1-6months">1 - 6 months</SelectItem>
                <SelectItem value="6-12months">6 - 12 months</SelectItem>
                <SelectItem value="1-2years">1 - 2 years</SelectItem>
                <SelectItem value="2-5years">2 - 5 years</SelectItem>
                <SelectItem value="5years+">5+ years</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Collateral */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>Collateral</label>
            <Select>
              <SelectTrigger style={{
                height: '3rem',
                borderColor: '#D1D5DB',
                borderRadius: '0.5rem'
              }}>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="any">Any</SelectItem>
                <SelectItem value="required">Required</SelectItem>
                <SelectItem value="not-required">Not Required</SelectItem>
                <SelectItem value="optional">Optional</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Approval Speed */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>Approval Speed</label>
            <Select>
              <SelectTrigger style={{
                height: '3rem',
                borderColor: '#D1D5DB',
                borderRadius: '0.5rem'
              }}>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="any">Any</SelectItem>
                <SelectItem value="instant">Instant</SelectItem>
                <SelectItem value="same-day">Same Day</SelectItem>
                <SelectItem value="1-3days">1 - 3 Days</SelectItem>
                <SelectItem value="1week">1 Week</SelectItem>
                <SelectItem value="2weeks+">2+ Weeks</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </div>
  )
}
