"use client"

import { useState } from "react"
import Image from "next/image"
import { Star } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import LoanDetailsPopup from "../loan-popup/loan-popup"

interface LoanDetails {
  loanAmount: string
  repaymentPeriod: string
  monthlyRepayment: string
  processingFee: string
  latePaymentFee: string
  minCreditScore: string
  stableIncome: string
  employmentHistory: string
  approvalTime: string
  lenderRating: number
  reviewCount: number
}

interface LoanOffer {
  id: string
  bankName: string
  logo: string
  rating: number
  amount: string
  interestRate: string
  duration: string
  collateral: string
  documents: string
  details: LoanDetails
}

const loanOffers: LoanOffer[] = [
  {
    id: "axis",
    bankName: "AXIS BANK",
    logo: "/Chromium.svg",
    rating: 4.8,
    amount: "₦100K - ₦2M",
    interestRate: "5% / month",
    duration: "1-6 months",
    collateral: "Required",
    documents: "ID, Utility Bill",
    details: {
      loanAmount: "₦100,000",
      repaymentPeriod: "6 months",
      monthlyRepayment: "₦17,167",
      processingFee: "1% of loan amount",
      latePaymentFee: "2% of outstanding balance",
      minCreditScore: "600",
      stableIncome: "₦50,000/month",
      employmentHistory: "6 months",
      approvalTime: "24-28 hrs",
      lenderRating: 4,
      reviewCount: 100,
    },
  },
  {
    id: "icic",
    bankName: "ICIC BANK",
    logo: "/DaVinci Resolve.svg",
    rating: 4.8,
    amount: "₦500K - ₦5M",
    interestRate: "6% / month",
    duration: "3-12 months",
    collateral: "Not Required",
    documents: "ID, CAC, Utility bill",
    details: {
      loanAmount: "₦500,000",
      repaymentPeriod: "12 months",
      monthlyRepayment: "₦45,833",
      processingFee: "1.5% of loan amount",
      latePaymentFee: "2.5% of outstanding balance",
      minCreditScore: "650",
      stableIncome: "₦100,000/month",
      employmentHistory: "12 months",
      approvalTime: "12-24 hrs",
      lenderRating: 4,
      reviewCount: 150,
    },
  },
  {
    id: "hay",
    bankName: "HAY BANK",
    logo: "/Fiat.svg",
    rating: 4.8,
    amount: "₦1M - ₦10M",
    interestRate: "4.5% / month",
    duration: "6-24 months",
    collateral: "Required",
    documents: "ID, CAC, Utility bill",
    details: {
      loanAmount: "₦1,000,000",
      repaymentPeriod: "24 months",
      monthlyRepayment: "₦48,750",
      processingFee: "0.5% of loan amount",
      latePaymentFee: "1.5% of outstanding balance",
      minCreditScore: "700",
      stableIncome: "₦200,000/month",
      employmentHistory: "18 months",
      approvalTime: "6-12 hrs",
      lenderRating: 4,
      reviewCount: 200,
    },
  },
]

export default function LoanOffers() {
  const [selectedOffer, setSelectedOffer] = useState<LoanOffer | null>(null)
  const [compareCount, setCompareCount] = useState(0)
  const [checkedOffers, setCheckedOffers] = useState<Set<string>>(new Set())

  const handleCompareChange = (offerId: string, checked: boolean) => {
    const newCheckedOffers = new Set(checkedOffers)
    if (checked) {
      newCheckedOffers.add(offerId)
    } else {
      newCheckedOffers.delete(offerId)
    }
    setCheckedOffers(newCheckedOffers)
    setCompareCount(newCheckedOffers.size)
  }

  const handleViewDetails = (offer: LoanOffer) => {
    setSelectedOffer(offer)
  }

  const handleClosePopup = () => {
    setSelectedOffer(null)
  }

  const handleCompareSelected = () => {
    // Implement comparison functionality here
    console.log("Comparing offers:", Array.from(checkedOffers))
    // You would typically navigate to a comparison page or open a comparison modal
  }

  return (
    <>
      <div className="py-12 px-4 md:px-8 max-w-[1400px] mx-auto">
        <div className="mb-8 flex justify-between items-center flex-wrap gap-4">
          <h2 className="text-2xl md:text-3xl font-bold text-[#1a1a1a]">Personal Loan Offers</h2>
          <Button 
            onClick={handleCompareSelected}
            className="bg-[#2D0A0A] text-white px-4 py-2 rounded-md flex items-center gap-2"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-5 h-5">
              <path d="M9 6H20M9 12H20M9 18H20M5 6L3 8L5 10M5 12L3 14L5 16" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <span className="font-medium">Compare Selected</span>
            <span className="bg-white text-black w-10 h-10 flex items-center justify-center rounded-md ml-2 font-bold">
              {compareCount}
            </span>
          </Button>
        </div>

        {/* Loan Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {loanOffers.map((offer) => (
            <div
              key={offer.id}
              className="bg-white rounded-3xl p-6 shadow-md border border-gray-100 flex flex-col h-fit"
            >
              {/* Bank Header */}
              <div className="flex items-center justify-between mb-6 flex-wrap gap-3">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center overflow-hidden">
                    <Image 
                      src={offer.logo} 
                      alt={offer.bankName} 
                      width={32} 
                      height={32}
                      className="w-8 h-8"
                    />
                  </div>
                  <span className="text-xl font-bold text-[#1a1a1a] break-words">
                    {offer.bankName}
                  </span>
                </div>
                <div className="flex items-center gap-1.5 flex-shrink-0">
                  <Star className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium text-[#1a1a1a]">{offer.rating}</span>
                </div>
              </div>

              {/* Loan Details */}
              <div className="flex flex-col gap-4 mb-6">
                {[
                  { label: "Loan Amount", value: offer.amount },
                  { label: "Interest Rate", value: offer.interestRate },
                  { label: "Duration", value: offer.duration },
                  { label: "Collateral", value: offer.collateral },
                  { label: "Documents", value: offer.documents },
                ].map((item, index) => (
                  <div key={index} className="flex justify-between items-start gap-4">
                    <span className="text-sm text-gray-500">{item.label}</span>
                    <span className="text-sm font-medium text-[#1a1a1a] text-right">{item.value}</span>
                  </div>
                ))}
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between gap-4 mt-auto">
                <Button
                  onClick={() => handleViewDetails(offer)}
                  className="bg-[#1a1a1a] text-white rounded-full py-3 px-6 text-base font-semibold border-none cursor-pointer flex-1 min-w-[120px] min-h-[44px]"
                >
                  View Details
                </Button>
                <div className="flex items-center gap-2 flex-shrink-0">
                  <Checkbox
                    checked={checkedOffers.has(offer.id)}
                    onCheckedChange={(checked) => handleCompareChange(offer.id, checked as boolean)}
                    className="w-[18px] h-[18px]"
                  />
                  <span className="text-sm text-gray-500 font-medium whitespace-nowrap">
                    Add to compare
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Fixed Compare Button for Mobile */}
      <div className="md:hidden fixed bottom-6 left-0 right-0 flex justify-center z-40">
        <Button 
          onClick={handleCompareSelected}
          className="bg-[#2D0A0A] text-white px-4 py-2 rounded-md flex items-center gap-2"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-5 h-5">
            <path d="M9 6H20M9 12H20M9 18H20M5 6L3 8L5 10M5 12L3 14L5 16" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <span className="font-medium">Compare Selected</span>
          <span className="bg-white text-black w-10 h-10 flex items-center justify-center rounded-md ml-2 font-bold">
            {compareCount}
          </span>
        </Button>
      </div>

      {/* Popup Component */}
      <LoanDetailsPopup isOpen={!!selectedOffer} onClose={handleClosePopup} loanOffer={selectedOffer} />
    </>
  )
}
