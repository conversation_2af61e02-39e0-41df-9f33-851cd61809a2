"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { useRouter, usePathname } from "next/navigation";

const borrowerNavItems = [
  { name: "Dashboard Home", href: "/dashboard/borrower", icon: "🏠", active: true },
  { name: "My Loans", href: "#", icon: "💼" },
  { name: "Marketplace", href: "#", icon: "🛒" },
  { name: "Notifications", href: "#", icon: "🔔", badge: 3 },
  { name: "Support", href: "#", icon: "💬" },
];

const lenderNavItems = [
  { name: "My Portfolio", href: "/dashboard/lender", icon: "💼" }, // briefcase
  { name: "Create Loan Offer", href: "#", icon: "➕" },
  { name: "Borrower Applications", href: "#", icon: "👥" }, // people
  { name: "Reports & Earnings", href: "#", icon: "📈" }, // chart
  { name: "Notifications", href: "#", icon: "🔔" }, // bell
  { name: "Support", href: "#", icon: "🎧" }, // headset/phone
];

export default function Sidebar() {
  const router = useRouter();
  const pathname = usePathname();
  const [showModal, setShowModal] = useState(false);

  // Determine mode from URL
  const isBorrower = pathname.includes("/dashboard/borrower");
  const navItems = isBorrower ? borrowerNavItems : lenderNavItems;

  function hasBadge(item: object): item is { badge: number } {
    return Object.prototype.hasOwnProperty.call(item, 'badge');
  }

  const handleSwitch = () => {
    setShowModal(false);
    const newMode = isBorrower ? "lender" : "borrower";
    router.push(`/dashboard/${newMode}`);
  };

  return (
    <aside className="w-64 min-h-screen bg-white border-r flex flex-col p-6 justify-between">
      <div>
        <div className="mb-8">
          <div className="font-bold text-xl">Kredxa</div>
          <div className="text-xs text-gray-500 mt-1">{isBorrower ? "Borrower Mode" : "Lender Mode"}</div>
        </div>
        <nav className="flex flex-col gap-2">
          {navItems.map((item) => (
            <Link key={item.name} href={item.href} className={`flex items-center px-4 py-2 rounded-lg text-base font-medium transition-colors text-gray-700 hover:bg-gray-100`}>
              <span className="mr-3 text-xl">{item.icon}</span>
              <span>{item.name}</span>
              {hasBadge(item) && typeof item.badge === 'number' && (
                <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5">{item.badge}</span>
              )}
            </Link>
          ))}
        </nav>
      </div>
      <div>
        <button
          className="w-full mt-8 py-2 px-4 rounded-lg bg-indigo-600 text-white font-semibold hover:bg-indigo-700 transition"
          onClick={() => setShowModal(true)}
        >
          {isBorrower ? "Switch to Lender" : "Switch to Borrower"}
        </button>
        {/* Confirmation Modal */}
        {showModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
            <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-sm">
              <h3 className="text-lg font-semibold mb-2">Confirm Switch</h3>
              <p className="mb-4">Are you sure you want to switch to {isBorrower ? "Lender" : "Borrower"} mode?</p>
              <div className="flex justify-end gap-2">
                <button
                  className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300"
                  onClick={() => setShowModal(false)}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 rounded bg-indigo-600 text-white hover:bg-indigo-700"
                  onClick={handleSwitch}
                >
                  Switch
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </aside>
  );
} 