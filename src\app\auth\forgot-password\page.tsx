import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";

export default function ForgotPasswordPage() {
  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <header className="flex justify-between items-center px-8 py-4 border-b bg-white">
        <div className="font-bold text-lg">Kredxa</div>
        <nav className="space-x-6 text-sm">
          <Link href="#">Home</Link>
          <Link href="#">FAQs</Link>
          <Link href="#">Contact</Link>
        </nav>
      </header>
      <main className="flex flex-1 items-center justify-center">
        <div className="flex w-full max-w-4xl bg-white rounded-xl shadow-lg overflow-hidden">
          {/* Left: Forgot Password Form */}
          <div className="w-full md:w-1/2 p-8 flex flex-col justify-center">
            <h2 className="text-2xl font-bold mb-2">Forgot Password?</h2>
            <p className="mb-6 text-gray-600 text-sm">Enter your email or phone to reset your password.</p>
            <form className="space-y-4">
              <Input type="text" placeholder="Enter your email or phone" />
              <Button className="w-full" type="submit">Send Reset Link</Button>
            </form>
            <div className="flex space-x-4 mt-6 text-xs text-gray-400">
              <Link href="/auth/login">Back to Sign In</Link>
              <Link href="#">FAQs</Link>
              <Link href="#">Terms & Conditions</Link>
              <Link href="#">Privacy Policy</Link>
            </div>
          </div>
          {/* Right: Welcome Panel */}
          <div className="hidden md:flex w-1/2 bg-gradient-to-br from-gray-900 to-gray-800 text-white flex-col items-center justify-center p-8 rounded-r-xl">
            <div className="flex flex-col items-center">
              <div className="mb-4">
                <svg width="40" height="40" fill="none" viewBox="0 0 24 24"><path fill="#fff" d="M12 2C6.48 2 2 6.48 2 12c0 5.52 4.48 10 10 10s10-4.48 10-10C22 6.48 17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-4.41 3.59-8 8-8s8 3.59 8 8c0 4.41-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/></svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Welcome to Kredxa</h3>
              <p className="mb-4 text-center text-sm text-gray-200">Your one-stop marketplace for loans. Fast, transparent, and secure for both borrowers and lenders.</p>
              <ul className="text-sm space-y-2">
                <li className="flex items-center"><span className="mr-2">✔</span>Bank-level security</li>
                <li className="flex items-center"><span className="mr-2">✔</span>Personal & SME loans</li>
                <li className="flex items-center"><span className="mr-2">✔</span>Trusted by leading lenders</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
      <footer className="bg-white border-t mt-8 px-8 py-6 flex flex-col md:flex-row justify-between items-center text-xs text-gray-500">
        <div className="mb-2 md:mb-0">
          <div className="font-semibold">Kredxa</div>
          <div>The modern loan marketplace for individuals & SMEs. Compare, apply and manage loans with confidence.</div>
        </div>
        <div className="flex flex-col md:flex-row md:space-x-8 items-center">
          <div className="mb-2 md:mb-0">
            <div className="font-semibold">Quick Links</div>
            <div><Link href="#">About</Link></div>
            <div><Link href="#">Terms & Conditions</Link></div>
            <div><Link href="#">Privacy Policy</Link></div>
            <div><Link href="#">Contact Us</Link></div>
          </div>
          <div className="flex space-x-4 mt-2 md:mt-0">
            <a href="#" aria-label="Twitter"><svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-2.47.69a4.3 4.3 0 0 0 1.88-2.37 8.59 8.59 0 0 1-2.72 1.04A4.28 4.28 0 0 0 16.11 4c-2.37 0-4.29 1.92-4.29 4.29 0 .34.04.67.11.99C7.69 9.13 4.07 7.38 1.64 4.7c-.37.64-.58 1.38-.58 2.17 0 1.5.76 2.82 1.92 3.6-.7-.02-1.36-.21-1.94-.53v.05c0 2.1 1.5 3.85 3.5 4.25-.36.1-.74.16-1.13.16-.28 0-.54-.03-.8-.08.54 1.7 2.1 2.94 3.95 2.97A8.6 8.6 0 0 1 2 19.54c-.29 0-.57-.02-.85-.05A12.13 12.13 0 0 0 8.29 21.5c7.55 0 11.68-6.26 11.68-11.68 0-.18-.01-.36-.02-.54A8.18 8.18 0 0 0 24 4.59a8.36 8.36 0 0 1-2.54.7z"/></svg></a>
            <a href="#" aria-label="Facebook"><svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24"><path d="M22.68 0H1.32C.59 0 0 .59 0 1.32v21.36C0 23.41.59 24 1.32 24h11.49v-9.29H9.69v-3.62h3.12V8.41c0-3.1 1.89-4.79 4.65-4.79 1.32 0 2.45.1 2.78.14v3.22h-1.91c-1.5 0-1.79.71-1.79 1.75v2.3h3.58l-.47 3.62h-3.11V24h6.09c.73 0 1.32-.59 1.32-1.32V1.32C24 .59 23.41 0 22.68 0z"/></svg></a>
            <a href="#" aria-label="Instagram"><svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.16c3.2 0 3.584.012 4.85.07 1.17.056 1.97.24 2.43.41.59.22 1.01.48 1.45.92.44.44.7.86.92 1.45.17.46.35 1.26.41 2.43.058 1.266.07 1.65.07 4.85s-.012 3.584-.07 4.85c-.056 1.17-.24 1.97-.41 2.43-.22.59-.48 1.01-.92 1.45-.44.44-.86.7-1.45.92-.46.17-1.26.35-2.43.41-1.266.058-1.65.07-4.85.07s-3.584-.012-4.85-.07c-1.17-.056-1.97-.24-2.43-.41-.59-.22-1.01-.48-1.45-.92-.44-.44-.7-.86-.92-1.45-.17-.46-.35-1.26-.41-2.43C2.172 15.784 2.16 15.4 2.16 12s.012-3.584.07-4.85c.056-1.17.24-1.97.41-2.43.22-.59.48-1.01.92-1.45.44-.44.86-.7 1.45-.92.46-.17 1.26-.35 2.43-.41C8.416 2.172 8.8 2.16 12 2.16zm0-2.16C8.736 0 8.332.012 7.052.07c-1.28.058-2.16.24-2.91.51-.8.29-1.48.67-2.15 1.34-.67.67-1.05 1.35-1.34 2.15-.27.75-.45 1.63-.51 2.91C.012 8.332 0 8.736 0 12c0 3.264.012 3.668.07 4.948.058 1.28.24 2.16.51 2.91.29.8.67 1.48 1.34 2.15.67.67 1.35 1.05 2.15 1.34.75.27 1.63.45 2.91.51C8.332 23.988 8.736 24 12 24c3.264 0 3.668-.012 4.948-.07 1.28-.058 2.16-.24 2.91-.51.8-.29 1.48-.67 2.15-1.34.67-.67 1.05-1.35 1.34-2.15.27-.75.45-1.63.51-2.91.058-1.28.07-1.684.07-4.948 0-3.264-.012-3.668-.07-4.948-.058-1.28-.24-2.16-.51-2.91-.29-.8-.67-1.48-1.34-2.15-.67-.67-1.35-1.05-2.15-1.34-.75-.27-1.63-.45-2.91-.51C15.668.012 15.264 0 12 0zm0 5.838a6.162 6.162 0 1 0 0 12.324 6.162 6.162 0 0 0 0-12.324zm0 10.162a3.999 3.999 0 1 1 0-7.998 3.999 3.999 0 0 1 0 7.998zm7.844-10.406a1.44 1.44 0 1 0 0 2.88 1.44 1.44 0 0 0 0-2.88z"/></svg></a>
          </div>
        </div>
        <div className="mt-4 md:mt-0">© 2024 Kredxa. All rights reserved.</div>
      </footer>
    </div>
  );
} 